import React, { useEffect } from 'react'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'

function Scene2() {
    useEffect(() => {
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            1000
        );

        scene.background = new THREE.Color(0x000000)
        // Shader material for animated noise on the box
        const uniforms = {
            u_time: { value: 0.0 }
        };

        const material = new THREE.ShaderMaterial({
            uniforms: uniforms,
            vertexShader: `
                varying vec2 vUv;
                void main() {
                    vUv = uv;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                varying vec2 vUv;
                uniform float u_time;

                // Simple 2D noise function
                float hash(vec2 p) {
                    return fract(sin(dot(p, vec2(127.1, 3511.7))) * 43758.5453123);
                }
                float noise(vec2 p) {
                    vec2 i = floor(p);
                    vec2 f = fract(p);
                    float a = hash(i);
                    float b = hash(i + vec2(1.0, 0.0));
                    float c = hash(i + vec2(0.0, 1.0));
                    float d = hash(i + vec2(1.0, 1.0));
                    vec2 u = f * f * (3.0 - 2.0 * f);
                    return mix(a, b, u.x) +
                           (c - a) * u.y * (1.0 - u.x) +
                           (d - b) * u.x * u.y;
                }

                // Amazing animated color palette using sin/cos and time
                vec3 palette(float t) {
                    // t in [0,1]
                    float r = 0.9 + 0.5 * cos(6.2831 * (t + 0.00 + 0.2 * sin(u_time * 0.7)));
                    float g = 0.5 + 0.5 * cos(6.2831 * (t + 0.33 + 0.2 * cos(u_time * 0.5)));
                    float b = 0.9 + 0.5 * cos(6.2831 * (t + 0.67 + 0.2 * sin(u_time * 0.9)));
                    return vec3(r, g, b);
                }

                void main() {
                    float t = u_time * 0.5;
                    vec2 uv = vUv * 20.0 + vec2(t, t * 0.7);
                    float n = noise(uv);

                    // Animate color palette with time and noise
                    float colorShift = 0.3 * sin(u_time * 0.8 + n * 6.2831);
                    float palIdx = fract(n + colorShift + 0.3 * sin(u_time + vUv.x * 2.0 + vUv.y * 2.0));
                    vec3 color = palette(palIdx);

                    // Add a glowing highlight
                    float highlight = smoothstep(0.7, 1.0, n);
                    color = mix(color, vec3(1.0, 1.0, 1.0), highlight * 0.5);

                    gl_FragColor = vec4(color, 1.0);
                }
            `,
            // side: THREE.DoubleSide
        });

        const geometry = new THREE.IcosahedronGeometry(1, 1, 1);
        const box = new THREE.Mesh(geometry, material);
        scene.add(box);

        const wireMat = new THREE.MeshBasicMaterial({
            color: 'black',
            wireframe: true,
            opacity: 0.1,
            transparent: true,
            polygonOffset: true,
            polygonOffsetFactor: -1,
            polygonOffsetUnits: -1
        });
        const wireMesh = new THREE.Mesh(geometry, wireMat)
        box.add(wireMesh)

        camera.position.z = 3;

        const canvas = document.querySelector('canvas');
        const renderer = new THREE.WebGLRenderer({ canvas, antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);

        const controls = new OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.025;

        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        let startTime = performance.now();

        function animate() {
            requestAnimationFrame(animate);
            uniforms.u_time.value = (performance.now() - startTime) * 0.001;
            box.rotation.y += 0.01;
            renderer.render(scene, camera);
            controls.update();
        }
        animate();
    }, []);

    return (
        <canvas></canvas>
    );
}

export default Scene2