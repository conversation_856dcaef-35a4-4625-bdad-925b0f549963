import React, { useEffect } from 'react'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'


function GlslShader() {
    useEffect(() => {
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);

        const vShader = `
        varing vec3 vPosition;
        void main(){
        vPosition = position;
        gl_Position = projectMatrix * modelViewmatrix * vec4(position, 1.0)
        }
        `

        const material = new THREE.MeshBasicMaterial({

        });
        const geometry = new THREE.BoxGeometry(1, 1, 1);
        const mesh = new THREE.Mesh(geometry, material);
        scene.add(mesh);

        camera.position.z = 3;

        const canvas = document.querySelector('canvas')
        const renderer = new THREE.WebGLRenderer({ canvas, antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);

        const controls = new OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.025;

        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        })

        function animate() {
            requestAnimationFrame(animate);
            renderer.render(scene, camera)
            controls.update();
        }
        animate();
    })
    return (
        <canvas></canvas>
    )
}

export default GlslShader