import React, { useEffect } from 'react'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'


function Scene() {
    useEffect(() => {
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        scene.background = new THREE.Color(0xfffdfa); // more offwhite

        // Example data to pass from JS to shader
        let time = 0.0;

        // Define uniforms object
        const uniforms = {
            u_time: { value: time },
            u_color1: { value: new THREE.Color(0x8e24cc) }, // Deep purple
            u_color2: { value: new THREE.Color(0x00aaff) }  // Blue for electric highlights (fixed typo)
        };

        // Custom shader material for purple electric flame covering the entire box
        const material = new THREE.ShaderMaterial({
            uniforms: uniforms,
            vertexShader: `
                varying vec2 vUv;
                void main() {
                    vUv = uv;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform float u_time;
                uniform vec3 u_color1;
                uniform vec3 u_color2;
                varying vec2 vUv;

                // Simple 2D noise function
                float hash(vec2 p) {
                    return fract(sin(dot(p, vec2(127.1, 311.7))) * 43758.5453123);
                }
                float noise(vec2 p) {
                    vec2 i = floor(p);
                    vec2 f = fract(p);
                    float a = hash(i);
                    float b = hash(i + vec2(1.0, 0.0));
                    float c = hash(i + vec2(0.0, 1.0));
                    float d = hash(i + vec2(1.0, 1.0));
                    vec2 u = f * f * (3.0 - 2.0 * f);
                    return mix(a, b, u.x) +
                           (c - a) * u.y * (1.0 - u.x) +
                           (d - b) * u.x * u.y;
                }

                void main() {
                    // Use vUv directly to cover the entire box face
                    vec2 uv = vUv;

                    // Animate noise for electric effect
                    float n = noise(uv * 5.0 + vec2(0.0, u_time * 2.0));
                    float electric = 0.9 + 0.9 * sin(u_time * 20.0 + uv.y * 10.0 + n * 10.0);

                    // Color blend: purple base, blue electric highlights
                    vec3 color = mix(u_color1, u_color2, pow(n * electric, 1.0));

                    // Full opacity to cover the box
                    float alpha = 1.0;

                    gl_FragColor = vec4(color, alpha);
                }
            `,
            transparent: false
        });

        const geometry = new THREE.BoxGeometry(1, 1, 1);
        const cube = new THREE.Mesh(geometry, material);
        scene.add(cube);

        camera.position.z = 2;

        const canvas = document.querySelector('canvas')
        const renderer = new THREE.WebGLRenderer({ canvas, antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);

        const controls = new OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.05;

        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight)
        })

        // let animationId;
        function animate() {
            time += 0.002;
            uniforms.u_time.value = time; // Pass updated time to shader
            renderer.render(scene, camera);
            controls.update();
            requestAnimationFrame(animate);
        }
        animate();
    });
    return (
        <canvas></canvas>
    )
}

export default Scene